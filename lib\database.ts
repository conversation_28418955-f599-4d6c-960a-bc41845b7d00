import { supabase, User, Venue, Booking, Review, Strike, VenueImage, TimeSlot, ForumPost, ForumOffer } from './supabase';

// User operations
export const userService = {
  async getUsers(userType?: string, status?: string) {
    let query = supabase.from('users').select('*');
    
    if (userType) {
      query = query.eq('user_type', userType);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getUserById(id: string) {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async updateUser(id: string, updates: Partial<User>) {
    const { data, error } = await supabase
      .from('users')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async approveUser(id: string, approvedBy: string) {
    return this.updateUser(id, {
      status: 'approved',
      updated_at: new Date().toISOString()
    });
  },

  async rejectUser(id: string) {
    return this.updateUser(id, {
      status: 'rejected',
      updated_at: new Date().toISOString()
    });
  },

  async approveUser(id: string, approvedBy: string) {
    return this.updateUser(id, {
      status: 'approved',
      approved_by: approvedBy,
      approved_at: new Date().toISOString()
    });
  },

  async rejectUser(id: string) {
    return this.updateUser(id, { status: 'rejected' });
  },

  async suspendUser(id: string) {
    return this.updateUser(id, { status: 'suspended' });
  }
};

// Venue operations
export const venueService = {
  async getVenues(status?: string, ownerId?: string) {
    let query = supabase
      .from('venues')
      .select(`
        *,
        owner:users!venues_owner_id_fkey(name, email, phone),
        venue_images(*)
      `);
    
    if (status) {
      query = query.eq('status', status);
    }
    
    if (ownerId) {
      query = query.eq('owner_id', ownerId);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async getVenueById(id: string) {
    const { data, error } = await supabase
      .from('venues')
      .select(`
        *,
        owner:users!venues_owner_id_fkey(name, email, phone),
        venue_images(*),
        reviews!reviews_venue_id_fkey(
          *,
          reviewer:users!reviews_reviewer_id_fkey(name)
        )
      `)
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async createVenue(venueData: Omit<Venue, 'id' | 'created_at' | 'updated_at'>) {
    // Remove frontend-only fields before inserting
    const { images, amenities, available_hours, price_per_hour, capacity, total_bookings, ...dbVenueData } = venueData as any;

    const { data, error } = await supabase
      .from('venues')
      .insert([dbVenueData])
      .select()
      .single();

    if (error) throw error;

    // Add default image if images were provided
    if (images && images.length > 0) {
      await this.addVenueImage(data.id, images[0], true, 1);
    }

    return data;
  },

  async addVenueImage(venueId: string, imageUrl: string, isPrimary: boolean = false, displayOrder: number = 1) {
    const { data, error } = await supabase
      .from('venue_images')
      .insert([{
        venue_id: venueId,
        image_url: imageUrl,
        is_primary: isPrimary,
        display_order: displayOrder
      }])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateVenue(id: string, updates: Partial<Venue>) {
    const { data, error } = await supabase
      .from('venues')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async approveVenue(id: string, approvedBy: string) {
    return this.updateVenue(id, {
      status: 'approved',
      approved_by: approvedBy,
      approved_at: new Date().toISOString()
    });
  },

  async rejectVenue(id: string) {
    return this.updateVenue(id, { status: 'rejected' });
  },

  async searchVenues(filters: {
    city?: string;
    priceRange?: [number, number];
    rating?: number;
    date?: string;
  }) {
    let query = supabase
      .from('venues')
      .select(`
        *,
        venue_images(*)
      `)
      .eq('status', 'approved');
    
    if (filters.city) {
      query = query.ilike('city', `%${filters.city}%`);
    }
    
    if (filters.priceRange) {
      query = query
        .gte('day_price_per_hour', filters.priceRange[0])
        .lte('day_price_per_hour', filters.priceRange[1]);
    }
    
    if (filters.rating) {
      query = query.gte('rating', filters.rating);
    }
    
    const { data, error } = await query.order('rating', { ascending: false });
    
    if (error) throw error;
    return data;
  }
};

// Booking operations
export const bookingService = {
  async getBookings(playerId?: string, venueId?: string, status?: string) {
    let query = supabase
      .from('bookings')
      .select(`
        *,
        player:users!bookings_player_id_fkey(name, email, phone),
        venue:venues!bookings_venue_id_fkey(name, address, phone)
      `);
    
    if (playerId) {
      query = query.eq('player_id', playerId);
    }
    
    if (venueId) {
      query = query.eq('venue_id', venueId);
    }
    
    if (status) {
      query = query.eq('status', status);
    }
    
    const { data, error } = await query.order('created_at', { ascending: false });
    
    if (error) throw error;
    return data;
  },

  async createBooking(bookingData: Omit<Booking, 'id' | 'created_at' | 'updated_at'>) {
    // For test accounts, we skip auth.getUser() check since they use mock authentication
    // In production, this should be properly authenticated

    // Validate that player_id is provided
    if (!bookingData.player_id) {
      throw new Error('Player ID is required');
    }

    // Verify the player exists in our users table
    const { data: player, error: playerError } = await supabase
      .from('users')
      .select('id, status')
      .eq('id', bookingData.player_id)
      .single();

    if (playerError || !player) {
      throw new Error('Invalid player ID');
    }

    if (player.status !== 'approved') {
      throw new Error('Player account must be approved to make bookings');
    }

    const { data, error } = await supabase
      .from('bookings')
      .insert([bookingData])
      .select()
      .single();

    if (error) {
      console.error('Booking creation error:', error);
      throw error;
    }
    return data;
  },

  async updateBookingStatus(id: string, status: string, response?: string) {
    const updates: any = { status };
    
    if (status === 'confirmed') {
      updates.confirmed_at = new Date().toISOString();
    }
    
    if (status === 'cancelled') {
      updates.cancelled_at = new Date().toISOString();
    }
    
    if (response) {
      updates.venue_response = response;
    }
    
    const { data, error } = await supabase
      .from('bookings')
      .update(updates)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  }
};

// Review operations
export const reviewService = {
  async createReview(reviewData: Omit<Review, 'id' | 'created_at' | 'updated_at'>) {
    const { data, error } = await supabase
      .from('reviews')
      .insert([reviewData])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getReviewsForPlayer(playerId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        venue:venues(name)
      `)
      .eq('reviewee_id', playerId)
      .eq('review_type', 'venue_to_player')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getReviewsForVenue(venueId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(name)
      `)
      .eq('venue_id', venueId)
      .eq('review_type', 'player_to_venue')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getReviewsForPlayer(playerId: string) {
    const { data, error } = await supabase
      .from('reviews')
      .select(`
        *,
        reviewer:users!reviews_reviewer_id_fkey(name),
        venue:venues!reviews_venue_id_fkey(name)
      `)
      .eq('reviewee_id', playerId)
      .eq('review_type', 'venue_to_player')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }
};

// Strike operations
export const strikeService = {
  async issueStrike(strikeData: Omit<Strike, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('strikes')
      .insert([strikeData])
      .select()
      .single();
    
    if (error) throw error;
    
    // Update strike count for user or venue
    if (strikeData.user_id) {
      await supabase.rpc('increment_user_strikes', { user_id: strikeData.user_id });
    }
    
    if (strikeData.venue_id) {
      await supabase.rpc('increment_venue_strikes', { venue_id: strikeData.venue_id });
    }
    
    return data;
  },

  async getStrikes(userId?: string, venueId?: string) {
    let query = supabase
      .from('strikes')
      .select(`
        *,
        issued_by_user:users!strikes_issued_by_fkey(name)
      `);

    if (userId) {
      query = query.eq('user_id', userId);
    }

    if (venueId) {
      query = query.eq('venue_id', venueId);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  }
};

// Time slot operations
export const timeSlotService = {
  async getTimeSlots(venueId: string, date?: string) {
    let query = supabase
      .from('time_slots')
      .select('*')
      .eq('venue_id', venueId);

    if (date) {
      query = query.eq('slot_date', date);
    }

    const { data, error } = await query.order('start_time', { ascending: true });

    if (error) throw error;
    return data;
  },

  async createTimeSlot(timeSlotData: Omit<TimeSlot, 'id' | 'created_at'>) {
    const { data, error } = await supabase
      .from('time_slots')
      .insert([timeSlotData])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async updateTimeSlot(id: string, updates: Partial<TimeSlot>) {
    const { data, error } = await supabase
      .from('time_slots')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async blockTimeSlot(id: string, reason: string) {
    return this.updateTimeSlot(id, {
      is_blocked: true,
      is_available: false,
      blocked_reason: reason
    });
  },

  async unblockTimeSlot(id: string) {
    return this.updateTimeSlot(id, {
      is_blocked: false,
      is_available: true,
      blocked_reason: undefined
    });
  }
};

// Forum operations
export const forumService = {
  async getForumPosts(filters?: {
    city?: string;
    skill_level?: string;
    status?: string;
    player_id?: string;
  }) {
    let query = supabase
      .from('forum_posts')
      .select(`
        *,
        booking:bookings!forum_posts_booking_id_fkey(
          *,
          venue:venues!bookings_venue_id_fkey(name, address, city, phone)
        ),
        player:users!forum_posts_player_id_fkey(name, phone, profile_image_url)
      `);

    if (filters?.status) {
      query = query.eq('status', filters.status);
    } else {
      query = query.eq('is_active', true);
    }

    if (filters?.player_id) {
      query = query.eq('player_id', filters.player_id);
    }

    if (filters?.skill_level) {
      query = query.eq('skill_level', filters.skill_level);
    }

    // Filter by city through the booking's venue
    if (filters?.city) {
      query = query.eq('booking.venue.city', filters.city);
    }

    const { data, error } = await query.order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getForumPostById(id: string) {
    const { data, error } = await supabase
      .from('forum_posts')
      .select(`
        *,
        booking:bookings!forum_posts_booking_id_fkey(
          *,
          venue:venues!bookings_venue_id_fkey(name, address, city, phone, facilities)
        ),
        player:users!forum_posts_player_id_fkey(name, phone, profile_image_url, city)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data;
  },

  async createForumPost(postData: Omit<ForumPost, 'id' | 'created_at' | 'updated_at'>) {
    // Validate input data
    if (!postData.title?.trim()) {
      throw new Error('Title is required');
    }

    if (!postData.booking_id) {
      throw new Error('Booking ID is required');
    }

    if (!postData.player_id) {
      throw new Error('Player ID is required');
    }

    if (!postData.looking_for_players || postData.looking_for_players < 1) {
      throw new Error('Must be looking for at least 1 player');
    }

    // For test accounts, we skip auth.getUser() check since they use mock authentication
    // In production, this should be properly authenticated

    // Validate that player_id is provided
    if (!postData.player_id) {
      throw new Error('Player ID is required');
    }

    // Verify the player exists in our users table
    const { data: player, error: playerError } = await supabase
      .from('users')
      .select('id, status')
      .eq('id', postData.player_id)
      .single();

    if (playerError || !player) {
      throw new Error('Invalid player ID');
    }

    if (player.status !== 'approved') {
      throw new Error('Player account must be approved to create forum posts');
    }

    // Check if booking exists and belongs to the user
    const { data: booking, error: bookingError } = await supabase
      .from('bookings')
      .select('player_id, status, booking_date')
      .eq('id', postData.booking_id)
      .single();

    if (bookingError || !booking) {
      throw new Error('Booking not found');
    }

    if (booking.player_id !== postData.player_id) {
      throw new Error('Cannot create forum post for another user\'s booking');
    }

    if (booking.status !== 'confirmed') {
      throw new Error('Can only create forum posts for confirmed bookings');
    }

    // Check if booking is in the future
    if (new Date(booking.booking_date) < new Date()) {
      throw new Error('Cannot create forum posts for past bookings');
    }

    // Check if a forum post already exists for this booking
    const { data: existingPost, error: existingError } = await supabase
      .from('forum_posts')
      .select('id')
      .eq('booking_id', postData.booking_id)
      .single();

    if (existingPost) {
      throw new Error('A forum post already exists for this booking');
    }

    const { data, error } = await supabase
      .from('forum_posts')
      .insert([postData])
      .select()
      .single();

    if (error) {
      console.error('Forum post creation error:', error);
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('A forum post already exists for this booking');
      }
      throw new Error('Failed to create forum post. Please try again.');
    }
    return data;
  },

  async updateForumPost(id: string, updates: Partial<ForumPost>) {
    const { data, error } = await supabase
      .from('forum_posts')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async closeForumPost(id: string) {
    return this.updateForumPost(id, {
      status: 'closed',
      is_active: false,
      closed_at: new Date().toISOString()
    });
  },

  async deleteForumPost(id: string) {
    const { error } = await supabase
      .from('forum_posts')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  }
};

// Forum offer operations
export const forumOfferService = {
  async getOffersForPost(forumPostId: string) {
    const { data, error } = await supabase
      .from('forum_offers')
      .select(`
        *,
        offering_player:users!forum_offers_offering_player_id_fkey(name, phone, profile_image_url, city)
      `)
      .eq('forum_post_id', forumPostId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getOffersForPlayer(playerId: string) {
    const { data, error } = await supabase
      .from('forum_offers')
      .select(`
        *,
        forum_post:forum_posts!forum_offers_forum_post_id_fkey(
          *,
          booking:bookings!forum_posts_booking_id_fkey(
            *,
            venue:venues!bookings_venue_id_fkey(name, address, city)
          ),
          player:users!forum_posts_player_id_fkey(name, phone)
        )
      `)
      .eq('offering_player_id', playerId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async getReceivedOffers(playerId: string) {
    const { data, error } = await supabase
      .from('forum_offers')
      .select(`
        *,
        offering_player:users!forum_offers_offering_player_id_fkey(name, phone, profile_image_url, city),
        forum_post:forum_posts!forum_offers_forum_post_id_fkey(
          *,
          booking:bookings!forum_posts_booking_id_fkey(
            *,
            venue:venues!bookings_venue_id_fkey(name, address, city)
          )
        )
      `)
      .eq('forum_post.player_id', playerId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async createOffer(offerData: Omit<ForumOffer, 'id' | 'created_at' | 'updated_at'>) {
    // Validate input data
    if (!offerData.forum_post_id) {
      throw new Error('Forum post ID is required');
    }

    if (!offerData.offering_player_id) {
      throw new Error('Offering player ID is required');
    }

    if (!offerData.players_count || offerData.players_count < 1) {
      throw new Error('Must offer at least 1 player');
    }

    // For test accounts, we skip auth.getUser() check since they use mock authentication
    // In production, this should be properly authenticated

    // Validate that offering_player_id is provided
    if (!offerData.offering_player_id) {
      throw new Error('Offering player ID is required');
    }

    // Verify the player exists in our users table
    const { data: player, error: playerError } = await supabase
      .from('users')
      .select('id, status')
      .eq('id', offerData.offering_player_id)
      .single();

    if (playerError || !player) {
      throw new Error('Invalid offering player ID');
    }

    if (player.status !== 'approved') {
      throw new Error('Player account must be approved to make offers');
    }

    // Check if forum post exists and is active
    const { data: forumPost, error: postError } = await supabase
      .from('forum_posts')
      .select('player_id, status, is_active, looking_for_players')
      .eq('id', offerData.forum_post_id)
      .single();

    if (postError || !forumPost) {
      throw new Error('Forum post not found');
    }

    if (!forumPost.is_active || forumPost.status !== 'open') {
      throw new Error('Cannot make offers on closed or inactive posts');
    }

    if (forumPost.player_id === offerData.offering_player_id) {
      throw new Error('Cannot make an offer on your own post');
    }

    // Validate players count doesn't exceed requirement
    if (offerData.players_count > forumPost.looking_for_players) {
      throw new Error(`Cannot offer more than ${forumPost.looking_for_players} players`);
    }

    // Check if user already made an offer for this post
    const { data: existingOffer, error: existingError } = await supabase
      .from('forum_offers')
      .select('id')
      .eq('forum_post_id', offerData.forum_post_id)
      .eq('offering_player_id', offerData.offering_player_id)
      .single();

    if (existingOffer) {
      throw new Error('You have already made an offer for this post');
    }

    const { data, error } = await supabase
      .from('forum_offers')
      .insert([offerData])
      .select()
      .single();

    if (error) {
      console.error('Forum offer creation error:', error);
      if (error.code === '23505') { // Unique constraint violation
        throw new Error('You have already made an offer for this post');
      }
      throw new Error('Failed to create offer. Please try again.');
    }
    return data;
  },

  async updateOfferStatus(id: string, status: string, responseMessage?: string) {
    const updates: any = {
      status,
      responded_at: new Date().toISOString()
    };

    if (responseMessage) {
      updates.response_message = responseMessage;
    }

    const { data, error } = await supabase
      .from('forum_offers')
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;

    // If accepting an offer, reject all other offers for the same post
    if (status === 'accepted') {
      await this.rejectOtherOffers(data.forum_post_id, id);

      // Also close the forum post
      await forumService.closeForumPost(data.forum_post_id);
    }

    return data;
  },

  async rejectOtherOffers(forumPostId: string, acceptedOfferId: string) {
    const { error } = await supabase
      .from('forum_offers')
      .update({
        status: 'rejected',
        responded_at: new Date().toISOString(),
        response_message: 'Another offer was accepted'
      })
      .eq('forum_post_id', forumPostId)
      .neq('id', acceptedOfferId)
      .eq('status', 'pending');

    if (error) throw error;
    return true;
  },

  async cancelOffer(id: string) {
    return this.updateOfferStatus(id, 'cancelled');
  },

  async deleteOffer(id: string) {
    const { error } = await supabase
      .from('forum_offers')
      .delete()
      .eq('id', id);

    if (error) throw error;
    return true;
  }
};
